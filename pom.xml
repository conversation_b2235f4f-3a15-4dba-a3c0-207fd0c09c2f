<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.4.10</version>
        <relativePath/>
    </parent>

    <groupId>cn.lili</groupId>
    <artifactId>lili-shop-parent</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <properties>
        <java.version>1.8</java.version>
        <revision>4.3</revision>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <skipTests>true</skipTests>
        <docker-registry>registry.cn-beijing.aliyuncs.com/lili-images</docker-registry>
        <images-version>1</images-version>
        <alipay-sdk-version>4.22.32.ALL</alipay-sdk-version>
        <mybatis-plus-version>3.5.5</mybatis-plus-version>
        <Hutool-version>5.8.24</Hutool-version>
        <TinyPinyin-verions>2.0.3.RELEASE</TinyPinyin-verions>
        <jasypt-version>3.0.4</jasypt-version>
        <neetl-version>2.9.10</neetl-version>
        <lombok-version>1.18.30</lombok-version>
        <redisson>3.15.6</redisson>
        <aliyun-version>4.5.18</aliyun-version>
        <aliyun-sdk-oss-version>3.11.1</aliyun-sdk-oss-version>
        <aliyun-sdk-dysms-version>2.0.8</aliyun-sdk-dysms-version>
        <aliyun-version>4.6.0</aliyun-version>
        <aliyun-sdk-oss-version>3.14.0</aliyun-sdk-oss-version>
        <aliyun-sdk-dysms-version>2.0.9</aliyun-sdk-dysms-version>
        <rocketmq-version>2.2.2</rocketmq-version>
        <jwt-version>0.11.2</jwt-version>
        <sharding-jdbc-version>4.0.0</sharding-jdbc-version>
        <druid-version>1.2.16</druid-version>
        <simple-http-version>1.0.3</simple-http-version>
        <antlr4-version>4.7.2</antlr4-version>
        <antlr4-runtime-version>4.7.2</antlr4-runtime-version>
        <knife4j.version>2.0.9</knife4j.version>
        <de.codecentric>2.6.6</de.codecentric>
        <userAgentUtils>1.21</userAgentUtils>
        <poi-version>5.2.3</poi-version>
        <poi-ooxml-version>5.2.3</poi-ooxml-version>
        <logstash-logback-encoder>7.1.1</logstash-logback-encoder>
        <zxing>3.5.0</zxing>
        <slf4j-api>1.7.36</slf4j-api>
        <xk-time>3.2.3</xk-time>
        <commons-text>1.9</commons-text>
        <enjoy.version>4.3</enjoy.version>
        <xxl-job>2.3.0</xxl-job>
        <spotify>1.2.2</spotify>
        <spring-boot-admin>2.3.1</spring-boot-admin>
        <owasp-java-html-sanitizer>20211018.2</owasp-java-html-sanitizer>
        <minio.version>8.0.3</minio.version>
        <huaweicloud-obs.version>3.20.6.2</huaweicloud-obs.version>
        <cos.version>5.6.97</cos.version>
        <tencentcloud.version>3.1.693</tencentcloud.version>
        <kuaidi100-api.version>1.0.11</kuaidi100-api.version>
    </properties>

    <modules>
        <module>framework</module>
        <module>buyer-api</module>
        <module>manager-api</module>
        <module>seller-api</module>
        <module>common-api</module>
        <module>consumer</module>
        <module>admin</module>
        <module>im-api</module>
    </modules>

    <build>
        <plugins>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.1.0</version>
            </plugin>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <version>${spotify}</version>
                <configuration>
                    <imageName>${docker-registry}/${project.artifactId}:${revision}.${images-version}</imageName>
                    <baseImage>java</baseImage>
                    <entryPoint>["java", "-jar", "/${project.build.finalName}.jar"]</entryPoint>
                    <resources>
                        <resource>
                            <targetPath>/</targetPath>
                            <directory>${project.build.directory}</directory>
                            <include>${project.build.finalName}.jar</include>
                        </resource>
                    </resources>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>${maven-javadoc-plugin.version}</version>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>central</id>
            <name>aliyun maven</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <layout>default</layout>
            <!-- 是否开启发布版构件下载 -->
            <releases>
                <enabled>true</enabled>
            </releases>
            <!-- 是否开启快照版构件下载 -->
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>
</project>