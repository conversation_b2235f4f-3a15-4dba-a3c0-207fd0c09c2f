package cn.lili.service;

import cn.lili.config.QwenConfig;
import cn.lili.controller.assistant.dto.ChatMessage;
import cn.lili.controller.assistant.dto.QwenApiRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.BufferedReader;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 简化版AI助手服务类 - 专用于测试
 */
@Service
public class SimpleAssistantService {

    private static final Logger logger = LoggerFactory.getLogger(SimpleAssistantService.class);

    @Autowired
    private QwenConfig qwenConfig;

    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper;

    // 会话上下文存储（内存中）
    private final Map<String, List<ChatMessage>> sessionContexts = new ConcurrentHashMap<>();

    public SimpleAssistantService() {
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
                .readTimeout(120, java.util.concurrent.TimeUnit.SECONDS)
                .writeTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
                .build();
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 健康检查
     */
    public Map<String, Object> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("service", "Qwen AI Assistant");
        health.put("timestamp", System.currentTimeMillis());
        
        Map<String, Object> config = new HashMap<>();
        config.put("model", qwenConfig.getModel());
        config.put("baseUrl", qwenConfig.getBaseUrl());
        config.put("maxContextLength", qwenConfig.getMaxContextLength());
        config.put("apiKeyConfigured", qwenConfig.getApiKey() != null && !qwenConfig.getApiKey().equals("YOUR_API_KEY_HERE"));
        health.put("config", config);
        
        return health;
    }

    /**
     * 简单聊天测试（非流式）
     */
    public String simpleChat(String message) throws IOException {
        List<ChatMessage> messages = new ArrayList<>();
        messages.add(new ChatMessage("system", qwenConfig.getSystemPrompt()));
        messages.add(new ChatMessage("user", message));

        QwenApiRequest request = new QwenApiRequest();
        request.setModel(qwenConfig.getModel());
        request.setMessages(messages);
        request.setStream(false); // 非流式
        request.setEnableSearch(true);

        String jsonBody = objectMapper.writeValueAsString(request);
        logger.info("发送请求: {}", jsonBody);

        RequestBody body = RequestBody.create(jsonBody, MediaType.get("application/json; charset=utf-8"));
        Request httpRequest = new Request.Builder()
                .url(qwenConfig.getBaseUrl() + "/chat/completions")
                .addHeader("Authorization", "Bearer " + qwenConfig.getApiKey())
                .addHeader("Content-Type", "application/json")
                .post(body)
                .build();

        try (Response response = httpClient.newCall(httpRequest).execute()) {
            if (!response.isSuccessful()) {
                String errorBody = response.body() != null ? response.body().string() : "";
                logger.error("API调用失败: {} {}, 响应体: {}", response.code(), response.message(), errorBody);
                throw new IOException("API调用失败: " + response.code() + " " + response.message() + ", 错误: " + errorBody);
            }

            String responseBody = response.body().string();
            logger.info("收到响应: {}", responseBody);

            // 解析响应
            Map<String, Object> jsonResponse = objectMapper.readValue(responseBody, Map.class);
            List<Map<String, Object>> choices = (List<Map<String, Object>>) jsonResponse.get("choices");
            if (choices != null && !choices.isEmpty()) {
                Map<String, Object> message = (Map<String, Object>) choices.get(0).get("message");
                return (String) message.get("content");
            }

            return "未收到有效响应";
        }
    }

    /**
     * 流式聊天
     */
    public SseEmitter streamChat(String sessionId, String userMessage) {
        SseEmitter emitter = new SseEmitter(120000L);

        new Thread(() -> {
            try {
                List<ChatMessage> context = getOrCreateContext(sessionId);
                context.add(new ChatMessage("user", userMessage));
                limitContextLength(context);
                callQwenApi(context, emitter);
            } catch (Exception e) {
                logger.error("流式聊天出错", e);
                try {
                    emitter.send(SseEmitter.event()
                            .name("error")
                            .data("错误: " + e.getMessage()));
                    emitter.complete();
                } catch (IOException ioException) {
                    logger.error("发送错误消息失败", ioException);
                }
            }
        }).start();

        return emitter;
    }

    private List<ChatMessage> getOrCreateContext(String sessionId) {
        return sessionContexts.computeIfAbsent(sessionId, k -> {
            List<ChatMessage> context = new ArrayList<>();
            context.add(new ChatMessage("system", qwenConfig.getSystemPrompt()));
            return context;
        });
    }

    private void limitContextLength(List<ChatMessage> context) {
        int maxLength = qwenConfig.getMaxContextLength();
        if (context.size() > maxLength) {
            ChatMessage systemMessage = context.get(0);
            List<ChatMessage> recentMessages = context.subList(context.size() - maxLength + 1, context.size());
            
            context.clear();
            context.add(systemMessage);
            context.addAll(recentMessages);
        }
    }

    private void callQwenApi(List<ChatMessage> context, SseEmitter emitter) throws IOException {
        QwenApiRequest request = new QwenApiRequest();
        request.setModel(qwenConfig.getModel());
        request.setMessages(context);
        request.setStream(true);
        request.setEnableSearch(true);

        // 设置流式输出选项
        Map<String, Object> streamOptions = new HashMap<>();
        streamOptions.put("include_usage", true);
        request.setStreamOptions(streamOptions);

        String jsonBody = objectMapper.writeValueAsString(request);
        logger.debug("流式请求: {}", jsonBody);

        RequestBody body = RequestBody.create(jsonBody, MediaType.get("application/json; charset=utf-8"));
        Request httpRequest = new Request.Builder()
                .url(qwenConfig.getBaseUrl() + "/chat/completions")
                .addHeader("Authorization", "Bearer " + qwenConfig.getApiKey())
                .addHeader("Content-Type", "application/json")
                .addHeader("Accept", "text/event-stream")
                .post(body)
                .build();

        try (Response response = httpClient.newCall(httpRequest).execute()) {
            if (!response.isSuccessful()) {
                String errorBody = response.body() != null ? response.body().string() : "";
                throw new IOException("API调用失败: " + response.code() + " " + response.message() + ", 错误: " + errorBody);
            }

            ResponseBody responseBody = response.body();
            if (responseBody == null) {
                throw new IOException("响应体为空");
            }

            StringBuilder assistantMessage = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(responseBody.charStream())) {
                String line;
                while ((line = reader.readLine()) != null) {
                    line = line.trim();
                    if (line.isEmpty()) continue;
                    
                    if (line.startsWith("data: ")) {
                        String data = line.substring(6).trim();
                        if ("[DONE]".equals(data)) break;

                        try {
                            Map<String, Object> jsonData = objectMapper.readValue(data, Map.class);
                            List<Map<String, Object>> choices = (List<Map<String, Object>>) jsonData.get("choices");
                            if (choices != null && !choices.isEmpty()) {
                                Map<String, Object> choice = choices.get(0);
                                Map<String, Object> delta = (Map<String, Object>) choice.get("delta");
                                
                                if (delta != null && delta.containsKey("content")) {
                                    String content = (String) delta.get("content");
                                    if (content != null && !content.isEmpty()) {
                                        assistantMessage.append(content);
                                        emitter.send(SseEmitter.event()
                                                .name("message")
                                                .data(content));
                                    }
                                }
                                
                                String finishReason = (String) choice.get("finish_reason");
                                if ("stop".equals(finishReason) || "length".equals(finishReason)) {
                                    break;
                                }
                            }
                        } catch (Exception e) {
                            logger.warn("解析流式数据失败: {}, 数据: {}", e.getMessage(), data);
                        }
                    }
                }
            }

            if (assistantMessage.length() > 0) {
                context.add(new ChatMessage("assistant", assistantMessage.toString()));
            }

            emitter.send(SseEmitter.event().name("done").data(""));
            emitter.complete();

        } catch (Exception e) {
            logger.error("调用Qwen API失败", e);
            try {
                emitter.send(SseEmitter.event()
                        .name("error")
                        .data("API调用失败: " + e.getMessage()));
                emitter.complete();
            } catch (IOException ioException) {
                logger.error("发送错误消息失败", ioException);
            }
        }
    }

    /**
     * 清除会话
     */
    public void clearSession(String sessionId) {
        sessionContexts.remove(sessionId);
    }

    /**
     * 获取会话历史
     */
    public List<ChatMessage> getSessionHistory(String sessionId) {
        return sessionContexts.getOrDefault(sessionId, new ArrayList<>());
    }
}
