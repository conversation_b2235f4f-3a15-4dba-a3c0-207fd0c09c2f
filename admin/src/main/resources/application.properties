# 应用程序名称
spring.application.name=SpringBootAdmin
# 应用程序端口
server.port=8000
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always
#账号密码
spring.security.user.name=admin
spring.security.user.password=admin
spring.mail.host=smtp.qq.com
# to和from都要配置，否则发送邮件时会报错
spring.boot.admin.notify.mail.to=<EMAIL>
spring.boot.admin.notify.mail.from=<EMAIL>
# 邮件的用户名和密码
spring.mail.username=<EMAIL>
spring.mail.password=abcdefg123456!@#$%^
# 日志文件路径
logging.file.path=lili-logs/admin
lili.data.logstash.server=***************:4560
# 文件格式
logging.pattern.file=%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(${PID}){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n%wEx
