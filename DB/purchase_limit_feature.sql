-- 为商品SKU表添加限购数量字段
ALTER TABLE li_goods_sku ADD COLUMN purchase_limit int DEFAULT 0 NOT NULL COMMENT '限购数量，0表示不限购';

-- 为商品SKU表添加注册天数限制字段
ALTER TABLE li_goods_sku ADD COLUMN register_days_limit int DEFAULT 0 NOT NULL COMMENT '注册天数限制，0表示不限制';

-- 为商品SKU表添加划线价/原价字段
ALTER TABLE li_goods_sku ADD COLUMN original_price decimal(10,2) DEFAULT NULL COMMENT '划线价/原价';

-- 为商品SKU表添加虚拟销量字段
ALTER TABLE li_goods_sku ADD COLUMN virtual_buy_count int DEFAULT 0 NOT NULL COMMENT '虚拟销量';

-- 添加索引以提高查询性能
-- 为订单表添加会员ID和订单状态的联合索引
ALTER TABLE li_order ADD INDEX idx_member_status (member_id, order_status);

-- 为订单项表添加SKU_ID和订单SN的联合索引
ALTER TABLE li_order_item ADD INDEX idx_sku_order (sku_id, order_sn);
