SET NAMES utf8mb4;

DROP TABLE IF EXISTS `li_goods_comment`;
CREATE TABLE `li_goods_comment` (
  `id` bigint NOT NULL COMMENT '评论ID',
  `goods_id` bigint NOT NULL COMMENT '商品ID',
  `member_id` bigint NOT NULL COMMENT '会员ID',
  `content` text NOT NULL COMMENT '评论内容',
  `has_image` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否有图片',
  `has_video` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否有视频',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-正常，0-删除',
  `like_count` int NOT NULL DEFAULT '0' COMMENT '点赞数',
  `reply_count` int NOT NULL DEFAULT '0' COMMENT '回复数',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY <PERSON>EY (`id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_member_id` (`member_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品评论表'; 