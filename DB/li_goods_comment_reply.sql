SET NAMES utf8mb4;

DROP TABLE IF EXISTS `li_goods_comment_reply`;
CREATE TABLE `li_goods_comment_reply` (
  `id` bigint NOT NULL COMMENT '回复ID',
  `comment_id` bigint NOT NULL COMMENT '评论ID',
  `member_id` bigint NOT NULL COMMENT '会员ID',
  `reply_to` bigint DEFAULT NULL COMMENT '回复目标ID',
  `content` varchar(500) NOT NULL COMMENT '回复内容',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-正常，0-删除',
  `like_count` int NOT NULL DEFAULT '0' COMMENT '点赞数',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_comment_id` (`comment_id`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_reply_to` (`reply_to`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论回复表'; 