SET NAMES utf8mb4;

DROP TABLE IF EXISTS `li_goods_comment_media`;
CREATE TABLE `li_goods_comment_media` (
  `id` bigint NOT NULL COMMENT '媒体ID',
  `comment_id` bigint NOT NULL COMMENT '评论ID',
  `type` varchar(10) NOT NULL COMMENT '类型：IMAGE-图片，VIDEO-视频',
  `url` varchar(255) NOT NULL COMMENT '媒体URL',
  `thumb_url` varchar(255) DEFAULT NULL COMMENT '缩略图URL',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_comment_id` (`comment_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论媒体表'; 