SET NAMES utf8mb4;

DROP TABLE IF EXISTS `li_goods_comment_like`;
CREATE TABLE `li_goods_comment_like` (
  `id` bigint NOT NULL COMMENT '点赞ID',
  `member_id` bigint NOT NULL COMMENT '会员ID',
  `target_id` bigint NOT NULL COMMENT '目标ID',
  `type` varchar(10) NOT NULL COMMENT '类型：COMMENT-评论，REPLY-回复',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_member_target` (`member_id`,`target_id`,`type`),
  KEY `idx_target` (`target_id`,`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论点赞表'; 