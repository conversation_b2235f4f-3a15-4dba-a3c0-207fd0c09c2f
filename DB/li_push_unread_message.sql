SET NAMES utf8mb4;

DROP TABLE IF EXISTS `li_push_unread_message`;
CREATE TABLE `li_push_unread_message` (
      `id` bigint NOT NULL COMMENT '消息ID',
      `user_id` bigint NOT NULL COMMENT '用户ID',
      `title` varchar(100) DEFAULT NULL COMMENT '消息标题',
      `content` varchar(500) DEFAULT NULL COMMENT '消息内容',
      `type` varchar(50) NOT NULL COMMENT '消息类型(ORDER_WAIT_PAY/ORDER_WAIT_SHIP等)',
      `extras` text COMMENT '附加信息(JSON格式)',
      `create_time` datetime NOT NULL COMMENT '创建时间',
      PRIMARY KEY (`id`),
      KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户未读消息表';